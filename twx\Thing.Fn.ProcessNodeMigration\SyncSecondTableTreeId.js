/**
 * @definition    SyncSecondTableTreeId
 * @description   同步更新二级表中的TREE_ID字段，将旧节点的TREE_ID更新为新节点的TREE_ID  作者: wanghq  生成时间: 2025-08-24 10:11:51
 * @implementation    {Script}
 *
 * @param    {NUMBER}    stageId    阶段型号ID
 *
 * @returns    {JSON}
 */

var res = {};

try {
    // 记录开始时间
    var startTime = new Date();

    // 1. 参数验证
    if (!stageId || stageId === null || stageId === undefined) {
        throw "stageId参数不能为空";
    }

    // 2. 获取最新一批次的节点变更记录
    var getLatestBatchSql = "SELECT MAX(BATCH_ID) AS LATEST_BATCH FROM NODE_CHANGE_RECORD WHERE STAGE_ID = " + stageId;
    var latestBatchResult = Things['Thing.DB.Oracle'].RunQuery({ sql: getLatestBatchSql });

    if (latestBatchResult.rows.length === 0 || !latestBatchResult.rows[0].LATEST_BATCH) {
        throw "未找到阶段ID " + stageId + " 的节点变更记录";
    }

    var latestBatchId = latestBatchResult.rows[0].LATEST_BATCH;

    // 3. 获取该批次的所有变更记录（只处理有实际数据变更的记录）
    var changeRecordsSql = "SELECT OLD_NODE_ID, NEW_NODE_ID, OLD_NODE_NAME, NEW_NODE_NAME, CHANGE_TYPE " +
        "FROM NODE_CHANGE_RECORD " +
        "WHERE BATCH_ID = '" + latestBatchId + "' AND STAGE_ID = " + stageId +
        " AND OLD_NODE_ID IS NOT NULL AND NEW_NODE_ID IS NOT NULL " +
        " AND OLD_NODE_ID != NEW_NODE_ID";

    var changeRecordsResult = Things['Thing.DB.Oracle'].RunQuery({ sql: changeRecordsSql });

    if (changeRecordsResult.rows.length === 0) {
        throw "没有需要处理的TREE_ID变更记录";
    }

    // 4. 构建变更映射关系
    var changeMapping = [];
    var treeIdMappings = {};

    for (var i = 0; i < changeRecordsResult.rows.length; i++) {
        var record = changeRecordsResult.rows[i];
        var oldTreeId = record.OLD_NODE_ID;
        var newTreeId = record.NEW_NODE_ID;

        changeMapping.push({
            oldTreeId: oldTreeId,
            newTreeId: newTreeId,
            oldNodeName: record.OLD_NODE_NAME,
            newNodeName: record.NEW_NODE_NAME,
            changeType: record.CHANGE_TYPE
        });

        treeIdMappings[oldTreeId] = newTreeId;
    }

    // 5. 获取所有以SECOND_TABLE开头的二级表名称
    var getSecondTablesSql = "SELECT DISTINCT TABLE_NAME FROM TABLE_CONFIG WHERE TABLE_NAME LIKE 'SECOND_TABLE%' ORDER BY TABLE_NAME";
    var secondTablesResult = Things['Thing.DB.Oracle'].RunQuery({ sql: getSecondTablesSql });

    if (secondTablesResult.rows.length === 0) {
        throw "未找到任何SECOND_TABLE开头的二级表";
    }

    // 6. 处理每个二级表 - 使用MERGE语句进行集合操作优化
    var processedTables = 0;
    var totalAffectedRecords = 0;
    var tableDetails = [];
    var errorDetails = [];

    // 6.1 提前构建 MERGE 语句中的 USING 子句源数据（只需构建一次）
    var usingClause = "";
    for (var i = 0; i < changeRecordsResult.rows.length; i++) {
        var record = changeRecordsResult.rows[i];
        if (i > 0) {
            usingClause += " UNION ALL ";
        }
        usingClause += "SELECT " + record.OLD_NODE_ID + " AS old_id, " + record.NEW_NODE_ID + " AS new_id FROM DUAL";
    }

    // 6.2 对每个二级表执行一次性MERGE更新
    for (var j = 0; j < secondTablesResult.rows.length; j++) {
        var tableName = secondTablesResult.rows[j].TABLE_NAME;
        var tableStartTime = new Date();

        try {
            // 6.3 动态构建 UPDATE + CASE 语句（避免 MERGE 的 ON 子句限制）
            var oldTreeIds = [];
            var caseWhenClause = "";

            for (var k = 0; k < changeRecordsResult.rows.length; k++) {
                var record = changeRecordsResult.rows[k];
                oldTreeIds.push(record.OLD_NODE_ID);

                if (k > 0) {
                    caseWhenClause += " ";
                }
                caseWhenClause += "WHEN " + record.OLD_NODE_ID + " THEN " + record.NEW_NODE_ID;
            }

            var updateSql = "UPDATE " + tableName +
                           " SET TREE_ID = CASE TREE_ID " + caseWhenClause + " END " +
                           " WHERE TREE_ID IN (" + oldTreeIds.join(',') + ")";

            // 6.4 执行一个命令完成所有更新
            Things['Thing.DB.Oracle'].RunCommand({sql: updateSql});

            // 6.5 获取实际影响的记录数（通过查询新TREE_ID的记录数）
            var newTreeIds = [];
            for (var m = 0; m < changeRecordsResult.rows.length; m++) {
                newTreeIds.push(changeRecordsResult.rows[m].NEW_NODE_ID);
            }

            var countSql = "SELECT COUNT(*) AS AFFECTED_COUNT FROM " + tableName +
                          " WHERE TREE_ID IN (" + newTreeIds.join(',') + ")";
            var countResult = Things['Thing.DB.Oracle'].RunQuery({sql: countSql});
            var affectedRecords = countResult.rows[0].AFFECTED_COUNT;

            if (affectedRecords > 0) {
                totalAffectedRecords += affectedRecords;
                processedTables++;
            }

            var tableEndTime = new Date();
            var tableExecutionTime = tableEndTime.getTime() - tableStartTime.getTime();

            tableDetails.push({
                tableName: tableName,
                status: affectedRecords > 0 ? "SUCCESS" : "SKIPPED",
                affectedRecords: affectedRecords,
                executionTime: tableExecutionTime,
                updatedMappings: affectedRecords > 0 ? changeMapping.length : 0,
                error: null
            });

        } catch (tableError) {
            var tableEndTime = new Date();
            var tableExecutionTime = tableEndTime.getTime() - tableStartTime.getTime();

            var errorMsg = "处理表 " + tableName + " 失败：" + tableError;
            errorDetails.push(errorMsg);

            tableDetails.push({
                tableName: tableName,
                status: "ERROR",
                affectedRecords: 0,
                executionTime: tableExecutionTime,
                updatedMappings: 0,
                error: errorMsg
            });

            logger.error("SyncSecondTableTreeId-" + errorMsg);
        }
    }

    // 7. 计算总执行时间
    var endTime = new Date();
    var totalExecutionTime = endTime.getTime() - startTime.getTime();

    // 8. 构造格式化的变更摘要
    var summary = "批次 " + latestBatchId + " 的二级表TREE_ID同步完成：" +
        "处理了 " + processedTables + " 张表，" +
        "总计影响 " + totalAffectedRecords + " 条记录，" +
        "执行时间 " + totalExecutionTime + "ms";

    if (errorDetails.length > 0) {
        summary += "，但有 " + errorDetails.length + " 张表处理失败";
    }

    // 9. 构造成功响应
    res.success = true;
    res.msg = "二级表TREE_ID同步完成";
    res.data = {
        stageId: stageId,
        batchId: latestBatchId,
        processedTables: processedTables,
        totalTables: secondTablesResult.rows.length,
        totalAffectedRecords: totalAffectedRecords,
        executionTime: totalExecutionTime,
        tableDetails: tableDetails,
        changeMapping: changeMapping,
        errors: errorDetails,
        summary: summary
    };

    if (errorDetails.length > 0) {
        res.msg += "，但有 " + errorDetails.length + " 张表处理失败";
    }

} catch (error) {
    res.success = false;
    var msg = "SyncSecondTableTreeId-二级表TREE_ID同步失败：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
