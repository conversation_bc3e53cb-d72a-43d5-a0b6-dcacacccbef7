/**
 * @definition    SyncQualityPlanTreeId
 * @description   同步更新QUALITY_PLAN质量策划表中的TREEID字段，将旧节点的TREEID更新为新节点的TREEID  作者: wanghq  生成时间: 2025-08-24 11:02:33
 * @implementation    {Script}
 *
 * @param    {NUMBER}    stageId    阶段型号ID
 *
 * @returns    {JSON}
 */

var res = {};

try {
    // 记录开始时间
    var startTime = new Date();
    
    // 1. 参数验证
    if (!stageId || stageId === null || stageId === undefined) {
        throw "stageId参数不能为空";
    }

    // 2. 获取最新一批次的节点变更记录
    var getLatestBatchSql = "SELECT MAX(BATCH_ID) AS LATEST_BATCH FROM NODE_CHANGE_RECORD WHERE STAGE_ID = " + stageId;
    var latestBatchResult = Things['Thing.DB.Oracle'].RunQuery({sql: getLatestBatchSql});
    
    if (latestBatchResult.rows.length === 0 || !latestBatchResult.rows[0].LATEST_BATCH) {
        throw "未找到阶段ID " + stageId + " 的节点变更记录";
    }
    
    var latestBatchId = latestBatchResult.rows[0].LATEST_BATCH;
    
    // 3. 获取该批次的所有变更记录（只处理有实际数据变更的记录）
    var changeRecordsSql = "SELECT OLD_NODE_ID, NEW_NODE_ID, OLD_NODE_NAME, NEW_NODE_NAME, CHANGE_TYPE " +
                          "FROM NODE_CHANGE_RECORD " +
                          "WHERE BATCH_ID = '" + latestBatchId + "' AND STAGE_ID = " + stageId + 
                          " AND OLD_NODE_ID IS NOT NULL AND NEW_NODE_ID IS NOT NULL " +
                          " AND OLD_NODE_ID != NEW_NODE_ID";
    
    var changeRecordsResult = Things['Thing.DB.Oracle'].RunQuery({sql: changeRecordsSql});
    
    if (changeRecordsResult.rows.length === 0) {
        throw "没有需要处理的TREEID变更记录";
    }
    
    // 4. 构建变更映射关系
    var changeMapping = [];
    
    for (var i = 0; i < changeRecordsResult.rows.length; i++) {
        var record = changeRecordsResult.rows[i];
        
        changeMapping.push({
            oldTreeId: record.OLD_NODE_ID,
            newTreeId: record.NEW_NODE_ID,
            oldNodeName: record.OLD_NODE_NAME,
            newNodeName: record.NEW_NODE_NAME,
            changeType: record.CHANGE_TYPE
        });
    }
    
    // 5. 处理QUALITY_PLAN表的TREEID更新
    var tableStartTime = new Date();
    var affectedRecords = 0;
    var errorMsg = null;
    
    try {
        // 5.1 动态构建 UPDATE + CASE 语句
        var oldTreeIds = [];
        var caseWhenClause = "";
        
        for (var j = 0; j < changeRecordsResult.rows.length; j++) {
            var record = changeRecordsResult.rows[j];
            oldTreeIds.push(record.OLD_NODE_ID);
            
            if (j > 0) {
                caseWhenClause += " ";
            }
            caseWhenClause += "WHEN " + record.OLD_NODE_ID + " THEN " + record.NEW_NODE_ID;
        }
        
        var updateSql = "UPDATE QUALITY_PLAN " +
                       " SET TREEID = CASE TREEID " + caseWhenClause + " END " +
                       " WHERE TREEID IN (" + oldTreeIds.join(',') + ")";
        
        // 5.2 执行更新命令
        Things['Thing.DB.Oracle'].RunCommand({sql: updateSql});
        
        // 5.3 获取实际影响的记录数
        var newTreeIds = [];
        for (var k = 0; k < changeRecordsResult.rows.length; k++) {
            newTreeIds.push(changeRecordsResult.rows[k].NEW_NODE_ID);
        }
        
        var countSql = "SELECT COUNT(*) AS AFFECTED_COUNT FROM QUALITY_PLAN " +
                      " WHERE TREEID IN (" + newTreeIds.join(',') + ")";
        var countResult = Things['Thing.DB.Oracle'].RunQuery({sql: countSql});
        affectedRecords = countResult.rows[0].AFFECTED_COUNT;
        
    } catch (updateError) {
        errorMsg = "处理QUALITY_PLAN表失败：" + updateError;
        logger.error("SyncQualityPlanTreeId-" + errorMsg);
    }
    
    // 6. 计算执行时间
    var tableEndTime = new Date();
    var tableExecutionTime = tableEndTime.getTime() - tableStartTime.getTime();
    var totalExecutionTime = tableEndTime.getTime() - startTime.getTime();
    
    // 7. 构造格式化的变更摘要
    var summary = "批次 " + latestBatchId + " 的QUALITY_PLAN表TREEID同步完成：" +
                 "影响 " + affectedRecords + " 条记录，" +
                 "执行时间 " + totalExecutionTime + "ms";
    
    if (errorMsg) {
        summary += "，但处理过程中出现错误";
    }
    
    // 8. 构造响应结果
    if (errorMsg) {
        res.success = false;
        res.msg = "QUALITY_PLAN表TREEID同步失败";
        res.data = {
            stageId: stageId,
            batchId: latestBatchId,
            affectedRecords: 0,
            executionTime: totalExecutionTime,
            changeMapping: changeMapping,
            error: errorMsg,
            summary: summary
        };
    } else {
        res.success = true;
        res.msg = "QUALITY_PLAN表TREEID同步完成";
        res.data = {
            stageId: stageId,
            batchId: latestBatchId,
            affectedRecords: affectedRecords,
            executionTime: totalExecutionTime,
            changeMapping: changeMapping,
            error: null,
            summary: summary
        };
    }

} catch (error) {
    res.success = false;
    var msg = "SyncQualityPlanTreeId-QUALITY_PLAN表TREEID同步失败：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
