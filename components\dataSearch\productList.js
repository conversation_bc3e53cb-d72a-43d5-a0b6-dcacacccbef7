//初始化layui对象
layui.use(['layer', 'form', 'upload', 'dropdown'], function () {
	layer = layui.layer;
	upload = layui.upload;
	form = layui.form;
	dropdown = layui.dropdown;
	device = layui.device();
});

//加载质量数据类型下拉框
var loadTypeSelect = function (treeId) {
	var loadIndex = layer.load();
	var cb_success = function (res) {
		layer.close(loadIndex);
		if (res.data.length > 0) {
			$('#productType').combobox({
				data: res.data,
				valueField: 'name',
				textField: 'name',
				editable: true,
				width: 300,
				panelHeight: 400,
				onSelect: function (record) {
					loadProductTable(record, treeId);
				}
			});
			$('#productType').combobox("select", res.data[0].name);
		} else {
			showMsg('数据加载中！');
		}
	}
	//请求失败的回调
	var cb_error = function (xhr, textStatus, errorThrown) {
		layer.close(loadIndex);
		layer.alert("加载质量数据类型下拉框出错！", {
			icon: 2
		});
	};
	twxAjax("Thing.Fn.SecondTable", "GetProductTypeByTreeId", {
		treeId: treeId,
		username: sessionStorage.getItem('username')
	}, true, cb_success, cb_error);
}

//显示页面的提示信息，并且隐藏页面其他元素
var showMsg = function (msg) {
	$("#msg").text(msg).show();
	$('#productTypeDiv').hide();
	$('#secondTableDiv').hide();
	$('#threeExcel').hide();
	$('#otherTableDiv').hide();
	$('#tbr').hide();
}

//显示二级表
var showSecondTable = function () {
	$("#msg").hide();
	$('#productTypeDiv').show();
	$('#secondTableDiv').show();
	// $('#secondTable').datagrid('resize');
	$('#threeExcel').hide();
	$('#otherTableDiv').hide();
	$('#tbr').show();
}

//显示三级表excel
var showThreeExcel = function () {
	$("#msg").hide();
	$('#productTypeDiv').show();
	$('#secondTableDiv').hide();
	$('#threeExcel').show();
	$('#otherTableDiv').hide();
	$('#tbr').show();
}

//显示其他质量数据表
var showOtherTable = function () {
	$("#msg").hide();
	$('#productTypeDiv').show();
	$('#secondTableDiv').hide();
	$('#threeExcel').hide();
	$('#tbr').hide();
	$('#otherTableDiv').show();
}

//加载表格数据
var loadProductTable = function (record, treeId) {
	var type = record.type; //表格类型 1：二、三级表 2：excel导入类型和质量统计数据
	if (type == '1') {
		loadType1Table(record, treeId);
		initTbrBtn(record, treeId);
	} else if (type == '2') {
		showOtherTable();
		new QuaTable(record, treeId);
	}
}

//获取选中的质量数据类型
var getProductTypeSelect = function () {
	var datas = $('#productType').combobox("getData");
	var value = $('#productType').combobox("getValue");
	var record;
	for (var i = 0; i < datas.length; i++) {
		if (value == datas[i].name) {
			record = datas[i];
			break;
		}
	}
	return record;
}

//以前的质量表对象
var QuaTable = function (record, treeId) {
	var othis = this;
	this.tableId = "otherTable";
	this.pageOptions = {
		pageSize: 30,
		pageNumber: 1
	};
	this.tableColumns = [];
	var tableType = record.ctype;
	this.tableName = '';
	if (tableType == 'electronic_components') {
		this.tableColumns = electronicComponentsColumns;
		this.tableName = "电子元器件汇总表";
	} else if (tableType == 'cable_insulation_test') {
		this.tableColumns = cableTestColumns;
		this.tableName = "电缆导通绝缘测试汇总表";
	} else if (tableType == 'heating_element_reinspection') {
		this.tableColumns = heatingReinspectionColumns;
		this.tableName = "加热片入所复验";
	} else if (tableType == 'heating_circuit_test') {
		this.tableColumns = heatingTestColumns;
		this.tableName = "加热回路测试";
	} else if (tableType == 'StandAlong') {
		this.tableColumns = standAlongColumns;
		this.tableName = "单机装星情况";
	} else if (tableType == 'ConnectorOnOff') {
		this.tableColumns = connectorOnOffColumns;
		this.tableName = "接插件连接固封情况";
	} else if (tableType == 'ConnectorOnOffTimes') {
		this.tableColumns = connectorOnOffTimesColumns;
		this.tableName = "接插件插拔次数统计";
	} else if (tableType == 'Heater') {
		this.tableColumns = heaterColumns;
		this.tableName = "星上加热器";
	} else if (tableType == 'HeatResist') {
		this.tableColumns = heatResistColumns;
		this.tableName = "热敏电阻实施";
	} else if (tableType == 'LayersOnOff') {
		this.tableColumns = layersOnOffColumns;
		this.tableName = "正样多层装配情况";
	} else if (tableType == 'StructuralAssembly') {
		this.tableColumns = structuralAssemblyColumns;
		this.tableName = "结构装配检测表";
	}
	this.renderTable = function () {
		var gridHeight = windowH - 90;
		$('#' + othis.tableId).datagrid({
			data: [],
			columns: othis.tableColumns,
			height: gridHeight,
			singleSelect: true,
			rownumbers: true,
			remoteSort: false,
			pagination: true,
			emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
			loadMsg: '正在加载数据...',
			striped: false,
			onDblClickRow: function (rowIndex, rowData) {
				if (rowData.RESULTID) {
					twxAjax('Thing.Fn.DataSearch', 'QueryTreeIdsByResultId', {
						resultId: rowData.RESULTID
					}, true, function (data) {
						locationTreeNodeUtil(data.rows, ztreeObj, 'TREEID', function (
							thisNode) { });
					});
				} else if (rowData.REF_DPID) {
					twxAjax('Thing.Fn.DataSearch', 'QueryTreeIdsByNodeCode', {
						nodeCode: rowData.REF_DPID
					}, true, function (data) {
						locationTreeNodeUtil(data.rows, ztreeObj, 'TREEID', function (
							thisNode) { });
					});
				}
			},
			onLoadSuccess: function (data) {
				changeWidth(othis.tableId);
				$('#' + othis.tableId).datagrid('loaded');
			}
		});
	};
	//初始化行号
	this.initLineNumbers = function () {
		var rowNumbers = $('.datagrid-cell-rownumber');
		var start = (othis.pageOptions.pageNumber - 1) * othis.pageOptions.pageSize;
		$(rowNumbers).each(function (index) {
			var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
			$(rowNumbers[index]).html("");
			$(rowNumbers[index]).html(row);
		});
	};

	//初始化分页组件
	this.initPagination = function (data) {
		$('#' + othis.tableId).datagrid('getPager').pagination({
			total: data.total,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: 1,
			buttons: [{
				iconCls: 'icon-refresh',
				handler: function () {
					othis.queryDataByPage(othis.pageOptions.pageSize, othis.pageOptions
						.pageNumber);
				}
			}],
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			showPageList: true,
			showRefresh: false,
			onSelectPage: function (pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				othis.pageOptions.pageNumber = pageNumber;
				othis.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function (pageNumber, pageSize) {
				//返回false可以在取消刷新操作
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			},
			onRefresh: function (pageNumber, pageSize) {
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			},
			onChangePageSize: function (pageSize) {
				//改变pageSize时触发
				othis.pageOptions.pageSize = pageSize;
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			}
		})
	};
	this.totalRecords = 0;
	this.dataLoadFlag = false;
	this.pageLoadFlag = false;
	this.paginationShow = function () {
		$('#' + othis.tableId).datagrid('getPager').pagination('refresh', {
			total: othis.totalRecords,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber
		});
		//重新初始化行号
		othis.initLineNumbers();
	};
	//初始化全部的记录条数
	this.initTotalRecords = function () {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function (data) {
			othis.pageLoadFlag = true;
			othis.totalRecords = data.rows[0].COUNT;
			if (othis.dataLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function () { };

		var parmas = {
			tableType: record.ctype,
			treeid: treeId,
			username: sessionStorage.getItem('username')
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.DataSearch', 'QueryDataSearchCount1', parmas, true, cb_success, cb_error);
	};

	//分页查询数据
	this.queryDataByPage = function (pageSize, pageNumber) {
		othis.totalRecords = 0;
		othis.dataLoadFlag = false;
		othis.pageLoadFlag = false;
		$('#' + othis.tableId).datagrid('loading');
		othis.initTotalRecords();
		var cb_success = function (data) {
			othis.dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + othis.tableId).datagrid('loadData', data.rows);
			if (othis.pageLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function () {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = {
			tableType: record.ctype,
			treeid: treeId,
			pageSize: pageSize,
			pageNumber: pageNumber,
			username: sessionStorage.getItem('username')
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.DataSearch', 'QueryDataSearch1', parmas, true, cb_success, cb_error);
	};

	this.renderTable();
	//初始化分页组件
	this.initPagination({
		total: 0
	});
	//显示第一页的数据
	this.queryDataByPage(this.pageOptions.pageSize, this.pageOptions.pageNumber);
}
//二级表对象
var SecondTable = function (record, treeId) {
	var othis = this;
	this.tableId = "secondTable";
	this.cmenu = undefined;
	this.pageOptions = {
		pageSize: 30,
		pageNumber: 1
	};
	this.renderTable = function () {
		var gridHeight = windowH - 90;
		var cb_success = function (data) {
			if (data.success) {
				var dealCol = dealColumns(JSON.parse(data.result), record.hasCertificate);
				$('#secondTable').datagrid({
					data: [],
					columns: dealCol.col,
					height: gridHeight,
					singleSelect: false,
					remoteSort: false,
					pagination: true,
					emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
					loadMsg: '正在加载数据...',
					striped: false,
					onDblClickRow: function (rowIndex, rowData) {
						twxAjax('Thing.Fn.DataSearch', 'QueryTreeIdsByTreeId', {
							treeId: rowData.TREE_ID
						}, true, function (data) {
							locationTreeNodeUtil(data.rows, ztreeObj, 'TREEID',
								function (thisNode) { });
						});
					},
					onHeaderContextMenu: function (e, field) {
						e.preventDefault();
						if (!othis.cmenu) {
							othis.cmenu = gridUtil.createColumnMenu(othis.tableId);
						}
						othis.cmenu.menu('show', {
							left: e.pageX,
							top: e.pageY
						});
					},
					onLoadSuccess: function (data) {
						var rows = data.rows;
						var $datagrid = $('#secondTable');
						if (rows.length > 0) {
							for (var i = 0; i < rows.length; i++) {
								var row = rows[i];
								var mergedInfo = row.mergedInfo;
								if (mergedInfo != "" && mergedInfo != undefined) {
									var mergeds = mergedInfo.split(",");
									for (var j = 0; j < mergeds.length; j++) {
										var merged = mergeds[j];
										var columnName = merged.split(":")[0];
										var rowspan = merged.split(":")[1];
										$datagrid.datagrid('mergeCells', {
											index: i,
											field: columnName,
											rowspan: rowspan
										});
									}
								}
							}
						}
						changeWidth('secondTable');
						$("#secondTableDiv .datagrid-body").css("overflow-x", "auto");
						$('#' + othis.tableId).datagrid('loaded');
						renderTableImageUpload();
					}
				});
			} else {
				layer.alert("表头获取失败！", {
					icon: 2
				});
			}
		}

		var cb_error = function () {
			layer.alert("表头获取失败！", {
				icon: 2
			});
		};

		var parmas = {
			id: record.id
		};
		twxAjax('Thing.Fn.SecondTable', 'GetSecondTableHeader', parmas, false, cb_success, cb_error);
	};
	//初始化分页组件
	this.initPagination = function (data) {
		$('#' + othis.tableId).datagrid('getPager').pagination({
			total: data.total,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: 1,
			buttons: [{
				iconCls: 'icon-refresh',
				handler: function () {
					othis.queryDataByPage(othis.pageOptions.pageSize, othis.pageOptions
						.pageNumber);
				}
			}],
			pageList: [10, 20, 30, 40, 50, 100, 150, 200],
			showPageList: true,
			showRefresh: false,
			onSelectPage: function (pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				othis.pageOptions.pageNumber = pageNumber;
				othis.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function (pageNumber, pageSize) {
				//返回false可以在取消刷新操作
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			},
			onRefresh: function (pageNumber, pageSize) {
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			},
			onChangePageSize: function (pageSize) {
				//改变pageSize时触发
				othis.pageOptions.pageSize = pageSize;
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			}
		})
	};
	this.totalRecords = 0;
	this.dataLoadFlag = false;
	this.pageLoadFlag = false;
	this.paginationShow = function () {
		$('#' + othis.tableId).datagrid('getPager').pagination('refresh', {
			total: othis.totalRecords,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber
		});
	};
	//初始化全部的记录条数
	this.initTotalRecords = function () {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function (data) {
			othis.pageLoadFlag = true;
			othis.totalRecords = data.rows[0].result;
			if (othis.dataLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function () { };

		var parmas = {
			table_config_id: record.id,
			table_config_name: record.name,
			processTreeId: treeId,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataCount', parmas, true, cb_success, cb_error);
	};

	//分页查询数据
	this.queryDataByPage = function (pageSize, pageNumber) {
		othis.totalRecords = 0;
		othis.dataLoadFlag = false;
		othis.pageLoadFlag = false;
		$('#' + othis.tableId).datagrid('loading');
		othis.initTotalRecords();
		var cb_success = function (data) {
			othis.dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + othis.tableId).datagrid('loadData', data.array);
			if (othis.pageLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function () {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = {
			table_config_id: record.id,
			table_config_name: record.name,
			processTreeId: treeId,
			type: record.ctype,
			pageSize: pageSize,
			pageNumber: pageNumber,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataPage', parmas, true, cb_success, cb_error);
	};

	this.renderTable();
	//初始化分页组件
	this.initPagination({
		total: 0
	});
	//显示第一页的数据
	this.queryDataByPage(this.pageOptions.pageSize, this.pageOptions.pageNumber);
}


//加载二、三级表类型的表数据
var loadType1Table = function (record, treeId) {
	if (record.ctype != '3') {
		showSecondTable();
		window.secondTable = new SecondTable(record, treeId);
	} else {
		showThreeExcel();
		showExcelHtml(record, treeId);
	}
}

//显示excel文件
var showExcelHtml = function (record, treeId) {
	var cb_success = function (data) {
		if (data.array.length == 0) {
			$("#threeExcel").empty();
			$("#threeExcel").append('<span style="color:red">数据加载中！</span>');
		} else {
			var filepath = data.array[0].filepath;
			$.ajax({
				type: "POST",
				url: fileHandlerUrl + "/table/excel/to/html?filepath=" + filepath,
				async: true,
				contentType: "application/x-www-form-urlencoded; charset=utf-8",
				success: function (data) {
					$("#threeExcel").empty();
					$("#threeExcel").append(data);
				}
			});
		}
	};
	var cb_error = function () {
		layer.alert('加载出错...', {
			icon: 2
		});
	};

	var parmas = {
		table_config_id: record.id,
		table_config_name: record.name,
		processTreeId: treeId,
		type: record.ctype,
		query: {
			queryUser: sessionStorage.getItem('username')
		}
	};
	twxAjax('Thing.Fn.SecondTable', 'QueryTableData', parmas, true, cb_success, cb_error);
}
//初始化按钮
var initTbrBtn = function (record, treeId) {
	var treeNode = ztreeObj.getNodeByParam("TREEID", treeId, null);
	if ((treeNode.NODETYPE == 'leaf') || (treeNode.NODETYPE == 'dir') && (!treeNode.ISPARENT)) {
		$('#tbr').show();
	} else {
		$('#tbr').hide();
	}
	// //导出二级表按钮点击事件
	$('#product-quality-export').unbind("click").bind('click', function () {
		if (record.ctype == '3') {
			exportThreeFile(record, treeId);
		} else {
			exportSecondExcel(record, treeId, 2);
		}
	});

	//电缆网元器件汇总表 需要显示导出全部的按钮
	// if (record.name == '电缆网元器件汇总表') {
	// 	$('#export-all-dlw').show();
	// 	$('#export-all-dlw').unbind("click").bind('click', function() {
	// 		exportSecondExcel(record, treeId, 1);
	// 	});
	// } else {
	// 	$('#export-all-dlw').hide();
	// }

	// //下载三级表模板按钮点击事件
	// $('#product-quality-download').unbind("click").bind('click', function() {
	// 	downloadThreeFile(record, treeId);
	// });

	dropdown.render({
		elem: '#upload_23_file',
		data: [{
			title: '上传三级表',
			id: 'table_upload_three'
		}, {
			title: '上传二级表',
			id: 'table_upload_second'
		}],
		click: function (data, othis) {
			if (data.id == 'table_upload_second') {
				uploadThreeFile(2);
			} else if (data.id == 'table_upload_three') {
				uploadThreeFile(3);
			}
		}
	});

	dropdown.render({
		elem: '#download_23_file',
		data: [{
			title: '下载三级表模板',
			id: 'table_download_three'
		}, {
			title: '下载二级表模板',
			id: 'table_download_second'
		}],
		click: function (data, othis) {
			if (data.id == 'table_download_second') {
				downloadTplFile(2);
			} else if (data.id == 'table_download_three') {
				downloadTplFile(3);
			}
		}
	});

	//确认质量数据 消除超差红色报警
	$('#product-quality-confirm').unbind("click").bind('click', function () {
		var sels = $('#secondTable').datagrid('getSelections');
		if (sels.length > 0) {
			//确认
			layer.confirm("确认会将超差数据提示消除，是否继续？", {
				icon: 3,
				title: '提示'
			},
				function (index) {
					var loadIndex = layer.msg('确认中，请稍等', {
						icon: 16,
						shade: 0.01,
						time: 0
					});;
					var tableName = sels[0]["TABLE_NAME"];
					var tableConfigId = sels[0]["TABLE_CONFIG_ID"];
					var idArr = [];
					for (var i = 0; i < sels.length; i++) {
						var sel = sels[i];
						idArr.push(sel['ID']);
					}
					var ids = idArr.join(",");

					var cb_success = function (res) {
						layer.close(loadIndex);
						if (res.success) {
							layer.msg(res.msg);
							secondTable.queryDataByPage(secondTable.pageOptions.pageSize,
								secondTable.pageOptions.pageNumber);
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					};
					var cb_error = function () {
						layer.close(loadIndex);
						layer.alert('确认出错...', {
							icon: 2
						});
					};

					twxAjax('Thing.Fn.SecondTable', 'ConfirmTableData', {
						tableConfigId: tableConfigId,
						tableName: tableName,
						treeId: treeId,
						ids: ids,
						fullname: sessionStorage.getItem('fullname'),
						username: sessionStorage.getItem('username')
					}, true, cb_success, cb_error);
				});
		} else {
			layer.alert('请至少选择一条数据！');
		}
	});

	//删除质量数据
	$('#product-quality-delete').unbind("click").bind('click', function () {
		var sels = $('#secondTable').datagrid('getSelections');
		if (sels.length > 0) {
			//确认是否需要删除数据
			layer.confirm("请确认是否要删除这" + sels.length + "条数据？", {
				icon: 3,
				title: '提示'
			},
				function (index) {
					var tableConfigId = sels[0]['TABLE_CONFIG_ID'];
					var tableName = sels[0]["TABLE_NAME"];
					var idArr = [];
					for (var i = 0; i < sels.length; i++) {
						var sel = sels[i];
						idArr.push(sel['ID']);
					}
					var ids = idArr.join(",");

					var cb_success = function (res) {
						if (res.success) {
							layer.msg(res.msg);
							loadType1Table(record, treeId);
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					};
					var cb_error = function () {
						layer.alert('删除出错...', {
							icon: 2
						});
					};

					twxAjax('Thing.Fn.SecondTable', 'DeleteTableData', {
						tableConfigId: tableConfigId,
						tableName: tableName,
						treeId: treeId,
						ids: ids
					}, true, cb_success, cb_error);
				});
		} else {
			layer.alert('请至少选择一条数据！');
		}
	});
	// {{ wanghq: Add - 提取公共弹框函数，消除重复代码 }}
	var showReassociationDialog = function(title, trees, onAssociation, record, treeId) {
		var isHasLeaf = trees.leaf.length > 0;
		var height = isHasLeaf ? '330px' : '280px';
		layer.open({
			title: title,
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ["450px", height],
			content: '<div id="reassociationContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['关联', '关闭'],
			yes: function (index, layero) {
				var newTreeId;
				if (isHasLeaf) {
					if (document.getElementById("leaf").value == '') {
						layer.alert("请选择过程节点");
						return false;
					} else {
						newTreeId = document.getElementById("leaf").value;
					}
				} else {
					if (document.getElementById("dir").value == '') {
						layer.alert("请选择专业节点");
						return false;
					} else {
						newTreeId = document.getElementById("dir").value;
					}
				}

				// 调用传入的关联操作回调函数
				onAssociation(newTreeId, record, treeId);
			},
			btn2: function (index, layero) {
				return true;
			},
			success: function () {
				var tpl = $("#reassociationHtml")[0].innerHTML;
				$("#reassociationContent").append(tpl);
				$("#reassociationContent").parent().css("overflow", "visible");
				if (isHasLeaf) {
					$("#leafDiv").show();
				} else {
					$("#leafDiv").hide();
				}
				//加载型号
				for (var i = 0; i < trees.product.length; i++) {
					$("#model").append('<option value="' + trees.product[i].TREEID +
						'">' + trees.product[i].NODENAME + '</option>');
				}
				form.render();
				form.on('select(model)', function (d) {
					$("#phase").empty();
					$("#dir").empty();
					$("#leaf").empty();
					$("#phase").append('<option value="">请选择</option>');
					for (var i = 0; i < trees.phase.length; i++) {
						if (trees.phase[i].PARENTID == document
							.getElementById("model").value) {
							$("#phase").append('<option value="' + trees
								.phase[i].TREEID + '">' + trees.phase[i]
									.NODENAME + '</option>');
						}
					}
					form.render();
				});

				form.on('select(phase)', function (d) {
					$("#dir").empty();
					$("#leaf").empty();
					$("#dir").append('<option value="">请选择</option>');
					for (var i = 0; i < trees.dir.length; i++) {
						if (trees.dir[i].PARENTID == document
							.getElementById("phase").value) {
							$("#dir").append('<option value="' + trees.dir[
								i].TREEID + '">' + trees.dir[i]
									.NODENAME + '</option>');
						}
					}
					form.render();
				});
				if (isHasLeaf) {
					form.on('select(dir)', function (d) {
						$("#leaf").empty();
						$("#leaf").append('<option value="">请选择</option>');
						for (var i = 0; i < trees.leaf.length; i++) {
							if (trees.leaf[i].PARENTID == document
								.getElementById("dir").value) {
								$("#leaf").append('<option value="' + trees
									.leaf[i].TREEID + '">' + trees.leaf[
										i].NODENAME + '</option>');
							}
						}
						form.render();
					});
				}
			}
		});
	};

	//重新关联
	$('#product-quality-reassociation').unbind("click").bind('click', function () {
		var sels = $('#secondTable').datagrid('getSelections');
		if (sels.length > 0) {
			// {{ wanghq: Add - 添加loading效果和按钮禁用状态，防止重复点击 }}
			var loadingIndex = layer.msg('正在加载中....', {
				icon: 16,
				shade: 0.01,
				time: 0
			});
			$('#product-quality-reassociation').prop('disabled', true);

			var tableId = sels[0]["TABLE_CONFIG_ID"];
			var dataIdArr = [];
			for (var i = 0; i < sels.length; i++) {
				dataIdArr.push(sels[i]['ID']);
			}
			var dataIds = dataIdArr.join(',');

			// {{ wanghq: Modify - 定义重新关联的操作回调函数 }}
			var onReassociation = function(newTreeId, record, treeId) {
				// {{ wanghq: Add - 为弹窗内的关联操作添加loading效果 }}
				var associationLoadingIndex = layer.msg('正在关联中，请稍等', {
					icon: 16,
					shade: 0.01,
					time: 0
				});

				twxAjax("Thing.Fn.SecondTable", "ReassociationData", {
					tableId: tableId,
					newTreeId: newTreeId,
					treeId: treeId,
					dataIds: dataIds
				}, true, function (res) {
					// {{ wanghq: Add - 关联操作成功回调中关闭loading }}
					layer.close(associationLoadingIndex);
					if (res.success) {
						layer.closeAll();
						layer.msg('关联成功');
						loadType1Table(record, treeId);
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				}, function (err) {
					// {{ wanghq: Add - 关联操作失败回调中关闭loading }}
					layer.close(associationLoadingIndex);
					layer.msg('关联失败');
				});
			};

			var cb_success = function (data) {
				// {{ wanghq: Add - 在成功回调中关闭loading并恢复按钮状态 }}
				layer.close(loadingIndex);
				$('#product-quality-reassociation').prop('disabled', false);

				var res = data.rows[0].result;
				var trees = JSON.parse(res);

				// {{ wanghq: Modify - 使用公共弹框函数 }}
				showReassociationDialog('重新关联', trees, onReassociation, record, treeId);
			};
			var cb_error = function () {
				// {{ wanghq: Add - 在错误回调中关闭loading并恢复按钮状态 }}
				layer.close(loadingIndex);
				$('#product-quality-reassociation').prop('disabled', false);

				layer.alert('加载出错...', {
					icon: 2
				});
			};
			twxAjax("Thing.Fn.SecondTable", "QueryTreesByTableId", {
				tableId: tableId
			}, true, cb_success, cb_error);
		} else {
			layer.alert('请至少选择一条数据！');
		}
	});

	//一键关联按钮点击事件
	$('#product-quality-batch-reassociation').unbind("click").bind('click', function () {
		// {{ wanghq: Add - 获取当前选中的质量数据类型和树节点ID }}
		var record = getProductTypeSelect();
		if (!record) {
			layer.alert('请先选择质量数据类型！');
			return;
		}

		var selNodes = ztreeObj.getSelectedNodes();
		if (selNodes.length == 0) {
			layer.alert('请先选择树节点！');
			return;
		}
		var treeId = selNodes[0].TREEID;
		var tableId = record.id;

		// {{ wanghq: Add - 添加loading效果和按钮禁用状态，防止重复点击 }}
		var loadingIndex = layer.msg('正在加载中....', {
			icon: 16,
			shade: 0.01,
			time: 0
		});
		$('#product-quality-batch-reassociation').prop('disabled', true);

		// {{ wanghq: Modify - 定义一键关联的操作回调函数 }}
		var onBatchReassociation = function(newTreeId, record, treeId) {
			// {{ wanghq: Add - 为一键关联操作添加loading效果 }}
			var batchLoadingIndex = layer.msg('正在批量关联中....', {
				icon: 16,
				shade: 0.01,
				time: 0
			});

			twxAjax("Thing.Fn.SecondTable", "BatchReassociationData", {
				tableId: tableId,
				newTreeId: newTreeId,
				treeId: treeId
			}, true, function (res) {
				// {{ wanghq: Add - 批量关联操作成功回调中关闭loading }}
				layer.close(batchLoadingIndex);
				if (res.success) {
					layer.closeAll();
					layer.msg('批量关联成功');
					loadType1Table(record, treeId);
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			}, function (err) {
				// {{ wanghq: Add - 批量关联操作失败回调中关闭loading }}
				layer.close(batchLoadingIndex);
				layer.msg('批量关联失败');
			});
		};

		var cb_success = function (data) {
			// {{ wanghq: Add - 在成功回调中关闭loading并恢复按钮状态 }}
			layer.close(loadingIndex);
			$('#product-quality-batch-reassociation').prop('disabled', false);

			var res = data.rows[0].result;
			var trees = JSON.parse(res);

			// {{ wanghq: Modify - 使用公共弹框函数 }}
			showReassociationDialog('一键关联', trees, onBatchReassociation, record, treeId);
		};
		var cb_error = function () {
			// {{ wanghq: Add - 在错误回调中关闭loading并恢复按钮状态 }}
			layer.close(loadingIndex);
			$('#product-quality-batch-reassociation').prop('disabled', false);

			layer.alert('加载出错...', {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "QueryTreesByTableId", {
			tableId: tableId
		}, true, cb_success, cb_error);
	});

	//产品结构树关联点按钮击事件
	$('#product-quality-productLink').unbind("click").bind('click', function () {
		var sels = $('#secondTable').datagrid('getSelections');
		if (sels.length > 0) {
			layer.open({
				title: '产品结构树关联',
				type: 1,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				shadeClose: false,
				maxmin: false,
				resize: false, //不允许拉伸
				// maxmin: true,
				area: ["700px", '700px'],
				content: '<div id="productLinkContent" style="padding-top: 15px;padding-right: 15px;"><ul id="bomTree" class="ztree"></ul></div>',
				btn: ['关联', '关闭'],
				yes: function (index, layero) {
					var checkedTrees = bomTree.ztreeObj.getCheckedNodes();
					if (checkedTrees.length > 0) {

						var tableName = sels[0]["TABLE_NAME"];
						var dataIdArr = [];
						for (var i = 0; i < sels.length; i++) {
							dataIdArr.push(sels[i]['ID']);
						}
						var relationIds = dataIdArr.join(',');


						var treeIds = '';
						for (var i = 0; i < checkedTrees.length; i++) {
							if (checkedTrees[i].ID !== undefined) {
								treeIds += ',' + checkedTrees[i].ID;
							}
						}
						if (treeIds !== '') {
							treeIds = treeIds.substring(1);
						}

						twxAjax("Thing.Fn.BOM", "ProductLinkList", {
							relationIds: relationIds,
							treeIds: treeIds,
							relationUser: sessionStorage.getItem('username'),
							relationName: tableName
						}, true, function (res) {
							if (res.success) {
								layer.closeAll();
								layer.msg(res.msg);
							} else {
								layer.alert(res.msg, {
									icon: 2
								});
							}
						}, function (err) {
							layer.alert("关联请求失败！", {
								icon: 2
							});
						});
					} else {
						layer.alert('请勾选要关联的产品结构树节点！', {
							icon: 2
						});
						return false;
					}

				},
				btn2: function (index, layero) {
					return true;
				},
				success: function () {
					window.bomTree = new BomTree(true, false);
					bomTree.loadTree();
				}
			});
		} else {
			layer.alert('请至少选择一条数据！');
		}
	});

	//数据来源于MES显示手动同步
	if (record.mtype) {
		$('#product-quality-manualSync').show();
		//手动同步mes
		$('#product-quality-manualSync').unbind("click").bind('click', function () {
			var Iindex = layer.msg('正在同步,请不要关闭页面,请稍等......', {
				icon: 16,
				shade: 0.01,
				time: 0
			});
			var cb_success = function (data) {
				layer.close(Iindex);
				if (data.success) {
					layer.msg("成功导入" + data.result + "条数据！");
					loadType1Table(record, treeId);
				} else {
					layer.alert(data.msg, {
						icon: 2
					});
				}
			};
			var cb_error = function () {
				layer.close(Iindex);
				layer.alert('手动同步出错...', {
					icon: 2
				});
			};
			twxAjax("Thing.Fn.SecondTable", "manualSyncMes", {
				treeId: treeId,
				type: record.mtype,
				tableId: record.id,
				endY: record.endy,
				tableType: record.ctype
			}, true, cb_success, cb_error);
		});
	} else {
		$('#product-quality-manualSync').hide();
	}
}

//上传三级表
function uploadThreeFile(type) {
	var record = getProductTypeSelect();
	var treeId = ztreeObj.getSelectedNodes()[0].TREEID;
	var area = ['350px', '220px'];
	var fileFlag = false;

	var name = "";
	if (type == 3) {
		name = "三级表";
	} else if (type == 2) {
		name = "二级表";
	}
	layer.open({
		title: '导入' + record.name + name,
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: area,
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert('请选择需要导入的excel文件!', {
					icon: 2
				});
				return false;
			}

			if (record.ctype == "3") {
				var preFilename = getExcelName(record, treeId);
				var nowFilename = $("#selectedFileName").text();
				if (preFilename != '' && preFilename != nowFilename) {
					layer.alert('该节点已经上传过数据，如需更新，请上传文件：' + preFilename, {
						icon: 2
					});
					return false;
				}
			}

			var params = '';
			params += 'type=' + record.ctype;
			params += '&fileType=' + type;
			params += '&tableId=' + record.id;
			params += '&endY=' + (record.ctype == '3' ? "" : record.endy);
			params += '&treeId=' + treeId;
			params += '&creator=' + sessionStorage.getItem('username');
			uploadInst.config.url = fileHandlerUrl + '/table/import/three?' + params;
			if (device.ie && device.ie < 10) {
				$("form[target]")[0].action = fileHandlerUrl + '/table/import/three?' + params;
			}
			$('#uploadStart').click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			var addTpl = $("#uploadHtml")[0].innerHTML;
			$("#uploadContent").append(addTpl);
		}
	});

	form.render(null, 'uploadForm');
	var loading;
	var uploadInst = upload.render({
		elem: '#uploadChoice',
		url: fileHandlerUrl + '/table/import/three?',
		auto: false,
		accept: 'file',
		field: 'uploadFile',
		exts: 'xls|xlsx',
		bindAction: '#uploadStart',
		dataType: "json",
		choose: function (obj) {
			fileFlag = true;
			var o = obj.pushFile();
			var filename = '';
			for (var k in o) {
				var file = o[k];
				filename = file.name;
			}
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		},
		before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
			loading = layer.load(); //上传loading
		},
		done: function (res, index, upload) {
			layer.close(loading);
			if (res.success) {
				layer.closeAll();
				layer.msg("成功导入" + res.result + "条数据！");
				loadProductTable(record, treeId);
				var selNodes = ztreeObj.getSelectedNodes();
				if (selNodes.length > 0) {
					var treeNode = selNodes[0];
					if (treeNode.NODESTATUS == "notstart") {
						var pNode = treeNode.getParentNode();
						ztreeObj.reAsyncChildNodes(pNode, 'refresh', false, function () {
							ztreeObj.selectNode(ztreeObj.getNodeByTId($("#" + treeNode.parentTId +
								" a[title='" + treeNode.NODENAME +
								"']").parent().attr('id')), false, true);
						});
					}
				}
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}
	});
	if (device.ie && device.ie < 10) {
		$("input[name='uploadFile']").change(function () {
			var filename = $(this).val();
			filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		});
	}
}

//获取选中节点 选中类型的第一行数据的上传的三级表的excel名称
var getExcelName = function (record, treeId) {
	var result = "";
	var cb_success = function (data) {
		if (data.array.length == 0) {

		} else {
			result = data.array[0].filename;
		}
	};
	var cb_error = function () {

	};

	var parmas = {
		table_config_id: record.id,
		table_config_name: record.name,
		processTreeId: treeId,
		type: record.ctype,
		query: {
			queryUser: sessionStorage.getItem('username')
		}
	};
	twxAjax('Thing.Fn.SecondTable', 'QueryTableData', parmas, false, cb_success, cb_error);
	return result;
}


//下载模板文件
function downloadTplFile(type) {
	var record = getProductTypeSelect();
	var treeId = ztreeObj.getSelectedNodes()[0].TREEID;
	var pathName = "";
	var fileName = "";
	if (type == 3) {
		pathName = "tpath";
		fileName = "三级表";
	} else if (type == 2) {
		pathName = "spath";
		fileName = "二级表";
		if (record.ctype == "4") {
			pathName = "tpath";
		}
	}
	if (record[pathName] != undefined && record[pathName] != '' && record[pathName] != null) {
		var filepath = record[pathName];
		var name = record.name + fileName + "模板.xlsx";
		var url = fileHandlerUrl + "/first/phase/download/file";
		var form = $("<form></form>").attr("action", url).attr("method", "post");
		form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value", name));
		form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value", filepath));
		form.appendTo('body').submit().remove();
	} else {
		layer.alert("该节点未上传" + fileName + "模板！", {
			icon: 2
		})
	}
}

//下载三级表模板文件
function downloadThreeFile() {
	var record = getProductTypeSelect();
	var treeId = ztreeObj.getSelectedNodes()[0].TREEID;
	if (record.tpath != undefined && record.tpath != '' && record.tpath != null) {
		var filepath = record.tpath;
		var name = record.name + "三级表模板.xlsx";
		var url = fileHandlerUrl + "/first/phase/download/file";
		var form = $("<form></form>").attr("action", url).attr("method", "post");
		form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value", name));
		form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value", filepath));
		form.appendTo('body').submit().remove();
	} else {
		layer.alert("该节点未上传三级表模板！", {
			icon: 2
		})
	}
}

//导出二级表excel
function exportSecondExcel(record, treeId, dlwIsAll) {
	var loading;
	var url = fileHandlerUrl + "/table/second/export";
	$.fileDownload(url, {
		httpMethod: 'POST',
		data: {
			"processTreeId": treeId,
			"tableId": record.id,
			"tableName": record.name,
			"dlwIsAll": dlwIsAll,
			"query": JSON.stringify({
				"queryUser": sessionStorage.getItem('username')
			})
		},
		prepareCallback: function (url) {
			loading = layer.msg("正在导出...", {
				icon: 16,
				shade: 0.3,
				time: 0
			});
		},
		abortCallback: function (url) {
			layer.close(loading);
			layer.msg("导出异常！！");
		},
		successCallback: function (url) {
			layer.close(loading);
		},
		failCallback: function (html, url) {
			layer.close(loading);
			layer.msg("导出失败！！");
		}
	});
}

//导出上传的三级表文件
var exportThreeFile = function (record, treeId) {
	var cb_success = function (data) {
		if (data.array.length == 0) {
			layer.alert('没有数据导出！', {
				icon: 2
			});
		} else {
			var filepath = data.array[0].FILEPATH;
			var name = data.array[0].FILENAME;
			var url = getFileDownloadUrl(filepath, name);
			window.open(url);
		}
	};
	var cb_error = function () {
		layer.alert('加载出错...', {
			icon: 2
		});
	};
	var parmas = {
		table_config_id: record.id,
		table_config_name: record.name,
		processTreeId: treeId,
		query: {
			queryUser: sessionStorage.getItem('username')
		}
	};
	twxAjax('Thing.Fn.SecondTable', 'QueryTableData', parmas, true, cb_success, cb_error);
}


//初始化质量数据显示页面
var initProductDataPage = function () {
	var treeId = 1;
	var nodename = '';
	var isSpecial1 = false;
	if ($.fn.zTree.getZTreeObj("dpTree") != null) {
		var selNodes = $.fn.zTree.getZTreeObj("dpTree").getSelectedNodes();
		if (selNodes.length > 0) {
			var treeNode = selNodes[0];
			treeId = treeNode.TREEID;
			nodename = getNodeName(treeNode.NODENAME);
			if ((treeNode.NODETYPE == 'leaf') || (treeNode.NODETYPE == 'dir') && (!treeNode.ISPARENT)) {
				if (nodename == '一般结构件') {
					showSpecial1Tabel();
					initSpecial1TbrBtn(treeId);
					loadSpecial1Tabel(treeId);
					isSpecial1 = true;
				}
			}
		}
	}
	if (!isSpecial1) {
		loadTypeSelect(treeId); //加载质量数据类型下拉框
	}
}

//初始化一般结构件的操作按钮
var initSpecial1TbrBtn = function (treeId) {
	// $('#product-quality-upload').unbind('click').bind('click', function() {
	// 	uploadSpecial1ThreeFile(treeId);
	// });

	// //下载三级表模板按钮点击事件
	// $('#product-quality-download').unbind("click").bind('click', function() {
	// 	downloadSpecial1ThreeFile();
	// });

	//导出二级表按钮点击事件
	$('#product-quality-export').unbind("click").bind('click', function () {
		exportSpecial1SecondExcel(treeId);
	});

}
//导出一般结构件二级表
var exportSpecial1SecondExcel = function (treeId) {
	var type = 6;
	var loading;
	var url = fileHandlerUrl + "/first/phase/export/excel2";
	$.fileDownload(url, {
		httpMethod: 'POST',
		data: {
			"treeId": treeId,
			"type": type,
			"fi": mergeObj.firstIndex,
			"si": mergeObj.secIndex
		},
		prepareCallback: function (url) {
			loading = layer.msg("正在导出...", {
				icon: 16,
				shade: 0.3,
				time: 0
			});
		},
		abortCallback: function (url) {
			layer.close(loading);
			layer.msg("导出异常！！");
		},
		successCallback: function (url) {
			layer.close(loading);
		},
		failCallback: function (html, url) {
			layer.close(loading);
			layer.msg("导出失败！！");
		}
	});

}

//上传一般结构件
var uploadSpecial1ThreeFile = function (treeId) {
	var type = 6;
	var area = ['400px', '320px'];

	var fileFlag = false;
	layer.open({
		title: '导入一般结构件三级表',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: area,
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert('请选择需要导入的excel文件!', {
					icon: 2
				});
				return false;
			}
			var params = '';
			params += 'type=' + type;
			params += '&treeId=' + treeId;
			params += '&creator=' + sessionStorage.getItem('username');
			var ctype = $("input[name='selectFile1']:checked").val();
			params += '&ctype=' + ctype;
			uploadInst.config.url = fileHandlerUrl + '/first/phase/import/excel2?' + params;
			if (device.ie && device.ie < 10) {
				$("form[target]")[0].action = fileHandlerUrl + '/first/phase/import/excel2?' + params;
			}
			$('#uploadStart').click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			var addTpl = $("#uploadHtml1")[0].innerHTML;
			$("#uploadContent").append(addTpl);
		}
	});

	form.render(null, 'uploadForm');

	var uploadInst = upload.render({
		elem: '#uploadChoice',
		url: fileHandlerUrl + '/first/phase/import/excel2?',
		auto: false,
		accept: 'file',
		field: 'uploadFile',
		exts: 'xls|xlsx',
		bindAction: '#uploadStart',
		dataType: "json",
		choose: function (obj) {
			fileFlag = true;
			var o = obj.pushFile();
			var filename = '';
			for (var k in o) {
				var file = o[k];
				filename = file.name;
			}
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		},
		before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
			// layer.load(); //上传loading
		},
		done: function (res, index, upload) {
			if (res.success) {
				layer.closeAll();
				layer.msg("成功导入" + res.result + "条数据！");
				var selNodes = ztreeObj.getSelectedNodes();
				if (selNodes.length > 0) {
					var treeNode = selNodes[0];
					loadSpecial1Tabel(treeId);
					if (treeNode.NODESTATUS == "notstart") {
						var pNode = treeNode.getParentNode();
						ztreeObj.reAsyncChildNodes(pNode, 'refresh', false, function () {
							ztreeObj.selectNode(ztreeObj.getNodeByTId($("#" + treeNode
								.parentTId + " a[title='" + treeNode.NODENAME +
								"']").parent().attr('id')), false, true);
						});
					}
				}
			} else {
				layer.alert('导入失败，日志:' + res.result, {
					icon: 2
				});
			}
		}
	});
	if (device.ie && device.ie < 10) {
		$("input[name='uploadFile']").change(function () {
			var filename = $(this).val();
			filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		});
	}
}

//下载一般结构件三级表模板
var downloadSpecial1ThreeFile = function () {
	layer.open({
		title: '选择文件',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['350px', '200px'],
		content: '<div id="selectFileContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			var filename = $("input[name='selectFile']:checked").val();
			var DownloadTemplateFile = function (filename) {
				var path =
					"C:\\Program Files\\Apache Software Foundation\\Tomcat 8.5\\webapps\\FileHandle\\excelTpl\\";
				path = path + filename + ".xlsx";
				var name = filename + ".xlsx";
				var url = fileHandlerUrl + "/first/phase/download/file";
				var form = $("<form></form>").attr("action", url).attr("method", "post");
				form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName")
					.attr("value", name));
				form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath")
					.attr("value", path));
				form.appendTo('body').submit().remove();
			}
			DownloadTemplateFile(filename);
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			var addTpl = $("#selectFileHtml")[0].innerHTML;
			$("#selectFileContent").append(addTpl);
		}
	});
	form.render(null, 'selectFileForm');
}

//显示一般结构件
var showSpecial1Tabel = function () {
	$("#msg").hide();
	$("#productTypeDiv").hide();
	$("#tbr").show();
	$("#threeExcel").hide();
	$("#otherTableDiv").hide();
	$("#secondTableDiv").show();
}

//一般结构件 合并单元格信息
var mergeObj = {
	firstIndex: -1,
	secIndex: -1
}

//特殊处理的节点： 一般结构件
var loadSpecial1Tabel = function (treeId) {

	var cb_success = function (data) {
		var tableColumns = [];
		if (data.rows.length > 0) {
			var firstRow = data.rows[0];
			tableColumns = col6(firstRow['VAL1'], firstRow['VAL2']);
		} else {
			tableColumns = col6('', '');
		}
		var gridHeight = windowH - 90;
		$('#secondTable').datagrid({
			data: data.rows,
			// fit: true,
			columns: tableColumns,
			height: gridHeight,
			emptyMsg: '<div style="color:red;font-size:14px;text-align:left;">数据加载中！</div>',
			loadMsg: '正在加载数据...',
			singleSelect: true,
			striped: false,
			onLoadSuccess: function (data) {
				var d = data.rows;
				var $datagrid = $('#secondTable');
				mergeObj.firstIndex = -1;
				mergeObj.secIndex = -1;
				if (d.length != 0) {
					for (var i = 0; i < d.length; i++) {
						var row = d[i];
						var ctype = row.VAL14;
						if (ctype == '1') {
							mergeObj.firstIndex = i;
							break;
						}
					}
					if (mergeObj.firstIndex != -1) {
						$datagrid.datagrid('insertRow', {
							index: mergeObj.firstIndex,
							row: {
								ROWNUM: '<span class="td-bold-left">一、扩热板、支架</span>'
							}
						});
						$datagrid.datagrid('mergeCells', {
							index: mergeObj.firstIndex,
							field: 'ROWNUM',
							colspan: 11
						});
					}
					for (var i = 0; i < d.length; i++) {
						var row = d[i];
						var ctype = row.VAL14;
						if (ctype == '2') {
							mergeObj.secIndex = i;
							break;
						}
					}
					if (mergeObj.secIndex != -1) {
						$datagrid.datagrid('insertRow', {
							index: mergeObj.secIndex,
							row: {
								ROWNUM: '<span class="td-bold-left">二、定位块、铲刮片等</span>'
							}
						});
						$datagrid.datagrid('mergeCells', {
							index: mergeObj.secIndex,
							field: 'ROWNUM',
							colspan: 11
						});
					}
				}
				$("#secondTableDiv .td-bold-left").each(function (i, n) {
					$(n).parent().css("text-align", "left");
				});

				changeWidth("secondTable");
			}
		});
	};
	var cb_error = function () {
		layer.alert('加载出错...', {
			icon: 2
		});
	};
	var parmas = {
		type: 6,
		treeId: treeId
	};
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.ProductQuality', 'QueryData', parmas, true, cb_success, cb_error);
}