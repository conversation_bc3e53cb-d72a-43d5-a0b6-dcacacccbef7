/**
 * @definition    UpdateProcessNodesByMapping
 * @description   根据节点映射关系更新过程结构树模板中的过程节点  作者: wanghq  生成时间: 2025-08-23 09:22:46
 * @implementation    {Script}
 *
 * @param    {NUMBER}    stageId    阶段型号ID，默认值为3（模板型号）
 *
 * @returns    {JSON}
 */

var res = {};

try {
    // 1. 参数验证和默认值设置
    if (!stageId || stageId === null || stageId === undefined) {
        stageId = 3; // 默认使用模板型号
    }

    // 2. 预查询指定阶段的所有节点ID集合，提高后续查询效率
    var templateTreeSql = "SELECT TREEID FROM DATAPACKAGETREE START WITH TREEID = " + stageId + " CONNECT BY PRIOR TREEID = PARENTID";
    var templateTreeResult = Things['Thing.DB.Oracle'].RunQuery({sql: templateTreeSql});

    var templateTreeIds = [];
    for (var t = 0; t < templateTreeResult.rows.length; t++) {
        templateTreeIds.push(templateTreeResult.rows[t].TREEID);
    }

    if (templateTreeIds.length === 0) {
        throw "未找到阶段树节点（STAGE_ID=" + stageId + "）";
    }

    var templateTreeIdStr = templateTreeIds.join(',');

    // 2. 获取节点映射关系
    var mappingResult = me.GetProcessNodeMapping();
    if (!mappingResult.success) {
        throw "获取节点映射关系失败：" + mappingResult.msg;
    }

    var mappings = mappingResult.data;
    var processedCount = 0;
    var errorDetails = [];

    // 生成批次ID，用于关联本次操作的所有变更记录
    var batchId = "BATCH_" + dateFormat(new Date(), "yyyyMMddHHmmss");
    var changeRecords = [];
    
    // 3. 遍历每个映射关系进行节点迁移
    for (var i = 0; i < mappings.length; i++) {
        var mapping = mappings[i];
        var newNode = mapping.newNode;
        var oldNodes = mapping.oldNodes;

        try {
            // 3.1 判断处理策略：比较新节点与第一个旧节点的父目录
            var firstOldNode = oldNodes[0];
            var isSameParent = (newNode.pName === firstOldNode.pName);

            if (isSameParent) {
                // 同目录策略：直接修改第一个旧节点的名称

                // 查找第一个旧节点的父节点
                var parentSql = "SELECT TREEID FROM DATAPACKAGETREE " +
                               "WHERE NODENAME = '" + firstOldNode.pName + "' AND NODETYPE = 'dir' " +
                               "AND TREEID IN (" + templateTreeIdStr + ")";
                var parentResult = Things['Thing.DB.Oracle'].RunQuery({sql: parentSql});

                if (parentResult.rows.length === 0) {
                    throw "未找到父节点：" + firstOldNode.pName;
                }

                var parentId = parentResult.rows[0].TREEID;

                // 查找第一个旧节点
                var firstOldNodeSql = "SELECT TREEID, NODESORT FROM DATAPACKAGETREE " +
                                     "WHERE NODENAME = '" + firstOldNode.name + "' AND PARENTID = " + parentId + " AND NODETYPE = 'leaf' " +
                                     "AND TREEID IN (" + templateTreeIdStr + ")";
                var firstOldNodeResult = Things['Thing.DB.Oracle'].RunQuery({sql: firstOldNodeSql});

                if (firstOldNodeResult.rows.length === 0) {
                    throw "未找到第一个旧节点：" + firstOldNode.pName + "/" + firstOldNode.name;
                }

                var firstOldNodeId = firstOldNodeResult.rows[0].TREEID;
                var originalSort = firstOldNodeResult.rows[0].NODESORT;

                // 修改第一个旧节点的名称为新节点名称
                var updateFirstNodeSql = "UPDATE DATAPACKAGETREE SET NODENAME = '" + newNode.name + "' WHERE TREEID = " + firstOldNodeId;
                Things['Thing.DB.Oracle'].RunCommand({sql: updateFirstNodeSql});

                // 记录节点变更信息（同目录重命名）
                var renameRecord = {
                    oldNodeId: firstOldNodeId,
                    newNodeId: firstOldNodeId, // 同一个节点，只是重命名
                    oldNodeName: firstOldNode.name,
                    newNodeName: newNode.name,
                    oldParentName: firstOldNode.pName,
                    newParentName: newNode.pName,
                    changeType: "RENAME"
                };
                changeRecords.push(renameRecord);

                // 删除其余旧节点（从第二个开始）
                for (var j = 1; j < oldNodes.length; j++) {
                    var otherOldNode = oldNodes[j];

                    // 查找其他旧节点的父节点
                    var otherParentSql = "SELECT TREEID FROM DATAPACKAGETREE " +
                                        "WHERE NODENAME = '" + otherOldNode.pName + "' AND NODETYPE = 'dir' " +
                                        "AND TREEID IN (" + templateTreeIdStr + ")";
                    var otherParentResult = Things['Thing.DB.Oracle'].RunQuery({sql: otherParentSql});

                    if (otherParentResult.rows.length > 0) {
                        var otherParentId = otherParentResult.rows[0].TREEID;

                        // 查找并删除其他旧节点
                        var otherOldNodeSql = "SELECT TREEID FROM DATAPACKAGETREE " +
                                             "WHERE NODENAME = '" + otherOldNode.name + "' AND PARENTID = " + otherParentId + " AND NODETYPE = 'leaf' " +
                                             "AND TREEID IN (" + templateTreeIdStr + ")";
                        var otherOldNodeResult = Things['Thing.DB.Oracle'].RunQuery({sql: otherOldNodeSql});

                        if (otherOldNodeResult.rows.length > 0) {
                            var otherOldNodeId = otherOldNodeResult.rows[0].TREEID;

                            // 记录节点变更信息（删除节点）
                            var mergeDeleteRecord = {
                                oldNodeId: otherOldNodeId,
                                newNodeId: firstOldNodeId, // 合并到第一个节点
                                oldNodeName: otherOldNode.name,
                                newNodeName: newNode.name,
                                oldParentName: otherOldNode.pName,
                                newParentName: newNode.pName,
                                changeType: "MERGE_DELETE"
                            };
                            changeRecords.push(mergeDeleteRecord);

                            var deleteOtherNodeSql = "DELETE FROM DATAPACKAGETREE WHERE TREEID = " + otherOldNodeId;
                            Things['Thing.DB.Oracle'].RunCommand({sql: deleteOtherNodeSql});
                        }
                    }
                }

            } else {
                // 跨目录策略：删除所有旧节点，在新目录下创建新节点

                // 查找新节点的父节点
                var newParentSql = "SELECT TREEID FROM DATAPACKAGETREE " +
                                  "WHERE NODENAME = '" + newNode.pName + "' AND NODETYPE = 'dir' " +
                                  "AND TREEID IN (" + templateTreeIdStr + ")";
                var newParentResult = Things['Thing.DB.Oracle'].RunQuery({sql: newParentSql});

                if (newParentResult.rows.length === 0) {
                    throw "未找到新节点的父节点：" + newNode.pName;
                }

                var newParentId = newParentResult.rows[0].TREEID;

                // 获取第一个旧节点的排序位置
                var firstOldParentSql = "SELECT TREEID FROM DATAPACKAGETREE " +
                                       "WHERE NODENAME = '" + firstOldNode.pName + "' AND NODETYPE = 'dir' " +
                                       "AND TREEID IN (" + templateTreeIdStr + ")";
                var firstOldParentResult = Things['Thing.DB.Oracle'].RunQuery({sql: firstOldParentSql});

                var inheritedSort = null;
                if (firstOldParentResult.rows.length > 0) {
                    var firstOldParentId = firstOldParentResult.rows[0].TREEID;
                    var getFirstOldSortSql = "SELECT NODESORT FROM DATAPACKAGETREE " +
                                            "WHERE NODENAME = '" + firstOldNode.name + "' AND PARENTID = " + firstOldParentId + " AND NODETYPE = 'leaf' " +
                                            "AND TREEID IN (" + templateTreeIdStr + ")";
                    var firstOldSortResult = Things['Thing.DB.Oracle'].RunQuery({sql: getFirstOldSortSql});

                    if (firstOldSortResult.rows.length > 0) {
                        inheritedSort = firstOldSortResult.rows[0].NODESORT;
                    }
                }

                // 检查新节点是否已存在
                var checkNewNodeSql = "SELECT TREEID FROM DATAPACKAGETREE " +
                                     "WHERE NODENAME = '" + newNode.name + "' AND PARENTID = " + newParentId + " AND NODETYPE = 'leaf' " +
                                     "AND TREEID IN (" + templateTreeIdStr + ")";
                var checkResult = Things['Thing.DB.Oracle'].RunQuery({sql: checkNewNodeSql});

                if (checkResult.rows.length === 0) {
                    // 创建新节点
                    var getMaxIdSql = "SELECT NVL(MAX(TREEID), 0) + 1 AS NEW_ID FROM DATAPACKAGETREE";
                    var maxIdResult = Things['Thing.DB.Oracle'].RunQuery({sql: getMaxIdSql});
                    var newNodeId = maxIdResult.rows[0].NEW_ID;

                    // 使用继承的排序位置，如果没有则使用新的排序位置
                    var newSort;
                    if (inheritedSort !== null) {
                        newSort = inheritedSort;
                    } else {
                        var getSortSql = "SELECT NVL(MAX(NODESORT), 0) + 1 AS NEW_SORT FROM DATAPACKAGETREE WHERE PARENTID = " + newParentId;
                        var sortResult = Things['Thing.DB.Oracle'].RunQuery({sql: getSortSql});
                        newSort = sortResult.rows[0].NEW_SORT;
                    }

                    // 插入新节点
                    var insertSql = "INSERT INTO DATAPACKAGETREE (TREEID, PARENTID, NODENAME, NODETYPE, NODESORT, NODESTATUS) " +
                                   "VALUES (" + newNodeId + ", " + newParentId + ", '" + newNode.name + "', 'leaf', " + newSort + ", 'ACTIVE')";
                    Things['Thing.DB.Oracle'].RunCommand({sql: insertSql});

                    // 记录节点变更信息（跨目录创建新节点）
                    var createRecord = {
                        oldNodeId: null, // 新创建的节点
                        newNodeId: newNodeId,
                        oldNodeName: null,
                        newNodeName: newNode.name,
                        oldParentName: null,
                        newParentName: newNode.pName,
                        changeType: "CREATE"
                    };
                    changeRecords.push(createRecord);
                }
            }
            
            // 3.4 跨目录策略：删除所有旧节点
            if (!isSameParent) {
                for (var k = 0; k < oldNodes.length; k++) {
                    var oldNode = oldNodes[k];

                    // 查找旧节点的父节点（基于预查询的模板树范围）
                    var oldParentSql = "SELECT TREEID FROM DATAPACKAGETREE " +
                                      "WHERE NODENAME = '" + oldNode.pName + "' AND NODETYPE = 'dir' " +
                                      "AND TREEID IN (" + templateTreeIdStr + ")";
                    var oldParentResult = Things['Thing.DB.Oracle'].RunQuery({sql: oldParentSql});

                    if (oldParentResult.rows.length > 0) {
                        var oldParentId = oldParentResult.rows[0].TREEID;

                        // 查找并删除旧节点（基于预查询的模板树范围）
                        var findOldNodeSql = "SELECT TREEID FROM DATAPACKAGETREE " +
                                            "WHERE NODENAME = '" + oldNode.name + "' AND PARENTID = " + oldParentId + " AND NODETYPE = 'leaf' " +
                                            "AND TREEID IN (" + templateTreeIdStr + ")";
                        var oldNodeResult = Things['Thing.DB.Oracle'].RunQuery({sql: findOldNodeSql});

                        if (oldNodeResult.rows.length > 0) {
                            var oldNodeId = oldNodeResult.rows[0].TREEID;

                            // 记录节点变更信息（跨目录删除旧节点）
                            var migrateDeleteRecord = {
                                oldNodeId: oldNodeId,
                                newNodeId: newNodeId, // 迁移到新节点
                                oldNodeName: oldNode.name,
                                newNodeName: newNode.name,
                                oldParentName: oldNode.pName,
                                newParentName: newNode.pName,
                                changeType: "MIGRATE_DELETE"
                            };
                            changeRecords.push(migrateDeleteRecord);

                            // 直接删除旧节点（leaf节点不存在子节点）
                            var deleteOldNodeSql = "DELETE FROM DATAPACKAGETREE WHERE TREEID = " + oldNodeId;
                            Things['Thing.DB.Oracle'].RunCommand({sql: deleteOldNodeSql});
                        }
                    }
                }
            }
            
            processedCount++;
            
        } catch (mappingError) {
            var errorMsg = "处理映射关系失败 [" + newNode.pName + "/" + newNode.name + "]：" + mappingError;
            errorDetails.push(errorMsg);
            logger.error("UpdateProcessNodesByMapping-" + errorMsg);
        }
    }
    
    // 4. 保存节点变更记录到数据库
    var saveChangeRecordsResult = null;
    if (changeRecords.length > 0) {
        // 将changeRecords数组转换为JSON字符串传递
        var changeRecordsJson = JSON.stringify(changeRecords);

        saveChangeRecordsResult = me.SaveNodeChangeRecords({
            changeRecords: changeRecordsJson /* STRING */,
            batchId: batchId /* STRING */,
            stageId: stageId /* NUMBER */
        });
    } else {
        logger.warn("UpdateProcessNodesByMapping-没有生成变更记录，跳过保存操作");
        saveChangeRecordsResult = {
            success: true,
            msg: "无变更记录需要保存",
            data: {
                batchId: batchId,
                stageId: stageId,
                totalRecords: 0,
                savedRecords: 0,
                errors: []
            }
        };
    }

    // 5. 同步关联表数据（仅在stageId=3时执行）
    var relationTableSyncResult = null;
    if (stageId === 3) {
        relationTableSyncResult = me.SyncRelationTableData();
    } else {
        relationTableSyncResult = {
            success: true,
            msg: "关联表数据同步已跳过（仅在stageId=3时执行）",
            data: {
                skipped: true,
                reason: "stageId不等于3"
            }
        };
    }

    // 6. 重新整理所有父目录下的节点排序号，确保连续性
    var reorderResult = me.ReorderTemplateTreeNodes({
        stageId: stageId /* NUMBER */
    });

    // 7. 自动同步质量确认模板的节点引用
    var templateSyncResult = me.SyncProcessNodeMapping({
        stageId: stageId /* NUMBER */
    });

    // 8. 自动同步MES数据包的节点引用
    var dataPackageSyncResult = me.SyncDataPackageMapping({
        stageId: stageId /* NUMBER */
    });

    // 9. 自动同步模板数据初始化的节点名称
    var templateDataInitSyncResult = me.SyncTemplateDataInit({
        stageId: stageId /* NUMBER */
    });

    // 10. 自动同步二级表中的TREE_ID字段
    var secondTableSyncResult = me.SyncSecondTableTreeId({
        stageId: stageId /* NUMBER */
    });

    // 11. 构造成功响应
    res.success = true;
    res.msg = "节点迁移完成，成功处理 " + processedCount + " 个映射关系";
    res.data = {
        stageId: stageId,
        processedCount: processedCount,
        totalMappings: mappings.length,
        errors: errorDetails,
        batchId: batchId,
        changeRecordsCount: changeRecords.length,
        saveChangeRecordsResult: saveChangeRecordsResult,
        relationTableSyncResult: relationTableSyncResult,
        reorderResult: reorderResult,
        templateSyncResult: templateSyncResult,
        dataPackageSyncResult: dataPackageSyncResult,
        templateDataInitSyncResult: templateDataInitSyncResult,
        secondTableSyncResult: secondTableSyncResult
    };

    if (errorDetails.length > 0) {
        res.msg += "，但有 " + errorDetails.length + " 个映射关系处理失败";
    }

    if (saveChangeRecordsResult && saveChangeRecordsResult.success) {
        res.msg += "，变更记录已保存";
    } else {
        res.msg += "，但变更记录保存失败";
    }

    if (relationTableSyncResult && relationTableSyncResult.success) {
        if (relationTableSyncResult.data && relationTableSyncResult.data.skipped) {
            res.msg += "，关联表数据同步已跳过";
        } else {
            res.msg += "，关联表数据已同步";
        }
    } else {
        res.msg += "，但关联表数据同步失败";
    }

    if (reorderResult && reorderResult.success) {
        res.msg += "，节点排序号已重新整理";
    } else {
        res.msg += "，但节点排序号整理失败";
    }

    if (templateSyncResult && templateSyncResult.success) {
        res.msg += "，质量确认模板已同步";
    } else {
        res.msg += "，但质量确认模板同步失败";
    }

    if (dataPackageSyncResult && dataPackageSyncResult.success) {
        res.msg += "，MES数据包已同步";
    } else {
        res.msg += "，但MES数据包同步失败";
    }

    if (templateDataInitSyncResult && templateDataInitSyncResult.success) {
        if (templateDataInitSyncResult.data && templateDataInitSyncResult.data.skipped) {
            res.msg += "，模板数据初始化已跳过";
        } else {
            res.msg += "，模板数据初始化已同步";
        }
    } else {
        res.msg += "，但模板数据初始化同步失败";
    }

    if (secondTableSyncResult && secondTableSyncResult.success) {
        if (secondTableSyncResult.data && secondTableSyncResult.data.totalAffectedRecords > 0) {
            res.msg += "，二级表TREE_ID已同步（影响" + secondTableSyncResult.data.totalAffectedRecords + "条记录）";
        } else {
            res.msg += "，二级表TREE_ID同步完成（无需更新）";
        }
    } else {
        res.msg += "，但二级表TREE_ID同步失败";
    }

} catch (error) {
    res.success = false;
    var msg = "UpdateProcessNodesByMapping-节点迁移失败：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
