/**
 * @definition    SyncPTableTreeId
 * @description   同步更新所有以P_开头的数据表中的TREEID字段，将旧节点的TREEID更新为新节点的TREEID  作者: wanghq  生成时间: 2025-08-24 11:19:53
 * @implementation    {Script}
 *
 * @param    {NUMBER}    stageId    阶段型号ID
 *
 * @returns    {JSON}
 */

var res = {};

try {
    // 记录开始时间
    var startTime = new Date();
    
    // 1. 参数验证
    if (!stageId || stageId === null || stageId === undefined) {
        throw "stageId参数不能为空";
    }

    // 2. 获取最新一批次的节点变更记录
    var getLatestBatchSql = "SELECT MAX(BATCH_ID) AS LATEST_BATCH FROM NODE_CHANGE_RECORD WHERE STAGE_ID = " + stageId;
    var latestBatchResult = Things['Thing.DB.Oracle'].RunQuery({sql: getLatestBatchSql});
    
    if (latestBatchResult.rows.length === 0 || !latestBatchResult.rows[0].LATEST_BATCH) {
        throw "未找到阶段ID " + stageId + " 的节点变更记录";
    }
    
    var latestBatchId = latestBatchResult.rows[0].LATEST_BATCH;
    
    // 3. 获取该批次的所有变更记录（只处理有实际数据变更的记录）
    var changeRecordsSql = "SELECT OLD_NODE_ID, NEW_NODE_ID, OLD_NODE_NAME, NEW_NODE_NAME, CHANGE_TYPE " +
                          "FROM NODE_CHANGE_RECORD " +
                          "WHERE BATCH_ID = '" + latestBatchId + "' AND STAGE_ID = " + stageId + 
                          " AND OLD_NODE_ID IS NOT NULL AND NEW_NODE_ID IS NOT NULL " +
                          " AND OLD_NODE_ID != NEW_NODE_ID";
    
    var changeRecordsResult = Things['Thing.DB.Oracle'].RunQuery({sql: changeRecordsSql});
    
    if (changeRecordsResult.rows.length === 0) {
        throw "没有需要处理的TREEID变更记录";
    }
    
    // 4. 构建变更映射关系
    var changeMapping = [];
    
    for (var i = 0; i < changeRecordsResult.rows.length; i++) {
        var record = changeRecordsResult.rows[i];
        
        changeMapping.push({
            oldTreeId: record.OLD_NODE_ID,
            newTreeId: record.NEW_NODE_ID,
            oldNodeName: record.OLD_NODE_NAME,
            newNodeName: record.NEW_NODE_NAME,
            changeType: record.CHANGE_TYPE
        });
    }
    
    // 5. 获取所有以P_开头的表名称
    var getPTablesSql = "bi";
    var pTablesResult = Things['Thing.DB.Oracle'].RunQuery({sql: getPTablesSql});
    
    if (pTablesResult.rows.length === 0) {
        throw "未找到任何P_开头的数据表";
    }
    
    // 6. 处理每个P_表 - 使用UPDATE + CASE语句进行集合操作优化
    var processedTables = 0;
    var totalAffectedRecords = 0;
    var tableDetails = [];
    var errorDetails = [];
    
    // 6.1 构建 UPDATE + CASE 语句的组件（只需构建一次）
    var oldTreeIds = [];
    var caseWhenClause = "";
    
    for (var j = 0; j < changeRecordsResult.rows.length; j++) {
        var record = changeRecordsResult.rows[j];
        oldTreeIds.push(record.OLD_NODE_ID);
        
        if (j > 0) {
            caseWhenClause += " ";
        }
        caseWhenClause += "WHEN '" + record.OLD_NODE_ID + "' THEN '" + record.NEW_NODE_ID + "'";
    }
    
    // 6.2 对每个P_表执行一次性UPDATE更新
    for (var k = 0; k < pTablesResult.rows.length; k++) {
        var tableName = pTablesResult.rows[k].TABLE_NAME;
        var tableStartTime = new Date();
        
        try {
            // 6.3 动态构建完整的 UPDATE + CASE 语句（注意TREEID是VARCHAR2类型）
            var oldTreeIdStrs = [];
            for (var m = 0; m < oldTreeIds.length; m++) {
                oldTreeIdStrs.push("'" + oldTreeIds[m] + "'");
            }
            
            var updateSql = "UPDATE " + tableName + 
                           " SET TREEID = CASE TREEID " + caseWhenClause + " END " +
                           " WHERE TREEID IN (" + oldTreeIdStrs.join(',') + ")";
            
            // 6.4 执行一个命令完成所有更新
            Things['Thing.DB.Oracle'].RunCommand({sql: updateSql});
            
            // 6.5 获取实际影响的记录数（通过查询新TREEID的记录数）
            var newTreeIds = [];
            for (var n = 0; n < changeRecordsResult.rows.length; n++) {
                newTreeIds.push("'" + changeRecordsResult.rows[n].NEW_NODE_ID + "'");
            }
            
            var countSql = "SELECT COUNT(*) AS AFFECTED_COUNT FROM " + tableName + 
                          " WHERE TREEID IN (" + newTreeIds.join(',') + ")";
            var countResult = Things['Thing.DB.Oracle'].RunQuery({sql: countSql});
            var affectedRecords = countResult.rows[0].AFFECTED_COUNT;
            
            if (affectedRecords > 0) {
                totalAffectedRecords += affectedRecords;
                processedTables++;
            }
            
            var tableEndTime = new Date();
            var tableExecutionTime = tableEndTime.getTime() - tableStartTime.getTime();
            
            tableDetails.push({
                tableName: tableName,
                status: affectedRecords > 0 ? "SUCCESS" : "SKIPPED",
                affectedRecords: affectedRecords,
                executionTime: tableExecutionTime,
                updatedMappings: affectedRecords > 0 ? changeMapping.length : 0,
                error: null
            });
            
        } catch (tableError) {
            var tableEndTime = new Date();
            var tableExecutionTime = tableEndTime.getTime() - tableStartTime.getTime();
            
            var errorMsg = "处理表 " + tableName + " 失败：" + tableError;
            errorDetails.push(errorMsg);
            
            tableDetails.push({
                tableName: tableName,
                status: "ERROR",
                affectedRecords: 0,
                executionTime: tableExecutionTime,
                updatedMappings: 0,
                error: errorMsg
            });
            
            logger.error("SyncPTableTreeId-" + errorMsg);
        }
    }
    
    // 7. 计算总执行时间
    var endTime = new Date();
    var totalExecutionTime = endTime.getTime() - startTime.getTime();
    
    // 8. 构造格式化的变更摘要
    var summary = "批次 " + latestBatchId + " 的P_表TREEID同步完成：" +
                 "处理了 " + processedTables + " 张表，" +
                 "总计影响 " + totalAffectedRecords + " 条记录，" +
                 "执行时间 " + totalExecutionTime + "ms";
    
    if (errorDetails.length > 0) {
        summary += "，但有 " + errorDetails.length + " 张表处理失败";
    }
    
    // 9. 构造成功响应
    res.success = true;
    res.msg = "P_表TREEID同步完成";
    res.data = {
        stageId: stageId,
        batchId: latestBatchId,
        processedTables: processedTables,
        totalTables: pTablesResult.rows.length,
        totalAffectedRecords: totalAffectedRecords,
        executionTime: totalExecutionTime,
        tableDetails: tableDetails,
        changeMapping: changeMapping,
        errors: errorDetails,
        summary: summary
    };
    
    if (errorDetails.length > 0) {
        res.msg += "，但有 " + errorDetails.length + " 张表处理失败";
    }

} catch (error) {
    res.success = false;
    var msg = "SyncPTableTreeId-P_表TREEID同步失败：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
